import React, { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import Image<PERSON>ithBasePath from "../../core/img/imagewithbasebath";
import Select from "react-select"
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PosModals from "../../core/modals/pos-modal/posModals";
import CartCounter from "../../core/common/counter/counter";
import categoryService from "../../core/services/category.service";
import productService from "../../core/services/product.service";
import {
  getOrders,
  addProductToOrders,
  updateProductQuantity,
  removeProductFromOrders,
  clearOrders,
  calculateOrderTotal,
  getDiscountSettings,
  saveDiscountSettings,
  getPaymentSummary,
  savePaymentSummary
} from "../../core/utils/orderStorage";
import {
  getCurrentOrderPayments,
  calculateRemainingBalance,
  formatPaymentCurrency,
  clearCurrentOrderPayments
} from "../../core/utils/paymentStorage";
import { initializeTaxCategories } from "../../core/utils/taxCategories";
import { formatCurrency } from "../../core/utils/currency";
import TaxMappingStatus from "../../core/components/TaxMappingStatus";
// Import debug utilities to make them available globally
import "../../core/utils/taxCategoryDatabaseDebug";
import "./pos-custom.css";


const Pos = () => {
 const [activeTab , setActiveTab] = useState('all')
 const Location = useLocation();
 const [showAlert, setShowAlert] = useState(true)
 const [categories, setCategories] = useState([])
 const [products, setProducts] = useState([])
 const [filteredProducts, setFilteredProducts] = useState([])
 const [loadingCategories, setLoadingCategories] = useState(false)
 const [loadingProducts, setLoadingProducts] = useState(false)
 const [categoryError, setCategoryError] = useState(null)
 const [productError, setProductError] = useState(null)
 const [searchQuery, setSearchQuery] = useState('')
 const [orderItems, setOrderItems] = useState([])
 const [orderTotal, setOrderTotal] = useState({
   subtotal: '0.00',
   tax: '0.00',
   taxRate: 0,
   discount: '0.00',
   serviceCharges: '0.00',
   voucher: '0.00',
   roundOffAmount: '0.00',
   total: '0.00',
   discountType: 'percentage',
   discountValue: 0
 })
 const [discountSettings, setDiscountSettings] = useState({
   discountType: 'percentage',
   discountValue: 0,
   customPercentage: 0
 })
 const [paymentSummary, setPaymentSummary] = useState({
   serviceCharges: 0,
   tax: 0,
   taxRate: 0,
   voucher: 0,
   roundOff: true,
   roundOffAmount: 0
 })
 const [currentOrderPayments, setCurrentOrderPayments] = useState([])
 const [paymentBalance, setPaymentBalance] = useState({
   orderTotal: 0,
   totalPaid: 0,
   remaining: 0,
   isFullyPaid: false,
   overpaid: 0
 })

 // Fetch categories from API
 useEffect(() => {
   const fetchCategories = async () => {
     try {
       setLoadingCategories(true)
       const response = await categoryService.getCategories()
       if (response.isSuccessful && response.result) {
         // The categories are in the result property
         setCategories(response.result)
       } else {
         setCategoryError('Failed to fetch categories')
       }
     } catch (err) {
       console.error('Error fetching categories:', err)
       setCategoryError(err.message || 'Failed to fetch categories')
     } finally {
       setLoadingCategories(false)
     }
   }

   fetchCategories()
 }, [])

 // Fetch products from API
 useEffect(() => {
   const fetchProducts = async () => {
     try {
       setLoadingProducts(true)
       const response = await productService.getProducts()
       if (response.isSuccessful && response.result) {
         // The products are in the result property
         setProducts(response.result)
         setFilteredProducts(response.result)
       } else {
         setProductError('Failed to fetch products')
       }
     } catch (err) {
       console.error('Error fetching products:', err)
       setProductError(err.message || 'Failed to fetch products')
     } finally {
       setLoadingProducts(false)
     }
   }

   fetchProducts()
 }, [])

 // Filter products when category changes
 useEffect(() => {
   if (activeTab === 'all') {
     setFilteredProducts(products)
   } else {
     // Log for debugging
     console.log('Active Tab:', activeTab);
     console.log('Products:', products);

     // Check different ways products might be associated with categories
     const productsWithMatchingId = products.filter(product => product.productCategoryId === activeTab);
     console.log('Products with matching productCategoryId:', productsWithMatchingId);

     const productsWithMatchingCategory = products.filter(product =>
       product.productCategory?.id === activeTab ||
       product.categoryId === activeTab ||
       product.category?.id === activeTab
     );
     console.log('Products with matching category object:', productsWithMatchingCategory);

     // Use a more flexible approach to filter products
     const filtered = products.filter(product =>
       product.productCategoryId === activeTab ||
       product.productCategory?.id === activeTab ||
       product.categoryId === activeTab ||
       product.category?.id === activeTab
     );

     setFilteredProducts(filtered)
   }
 }, [activeTab, products])

 // Handle search functionality
 useEffect(() => {
   if (searchQuery.trim() === '') {
     // If search query is empty, reset to category filter
     if (activeTab === 'all') {
       setFilteredProducts(products)
     } else {
       // Use the same flexible approach as in the category filter
       const filtered = products.filter(product =>
         product.productCategoryId === activeTab ||
         product.productCategory?.id === activeTab ||
         product.categoryId === activeTab ||
         product.category?.id === activeTab
       )
       setFilteredProducts(filtered)
     }
   } else {
     // Filter by search query
     const searchResults = products.filter(product =>
       product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       product.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       product.description?.toLowerCase().includes(searchQuery.toLowerCase())
     )
     setFilteredProducts(searchResults)
   }
 }, [searchQuery, products, activeTab])

 // Handle search input change
 const handleSearchChange = (e) => {
   setSearchQuery(e.target.value)
 }

 // Load orders, discount settings, payment summary, and payments from local storage on component mount
 useEffect(() => {
   const savedOrders = getOrders();
   setOrderItems(savedOrders);

   // Load discount settings
   const savedDiscountSettings = getDiscountSettings();
   setDiscountSettings(savedDiscountSettings);

   // Load payment summary settings
   const savedPaymentSummary = getPaymentSummary();
   setPaymentSummary(savedPaymentSummary);

   // Load current order payments
   const savedPayments = getCurrentOrderPayments();
   setCurrentOrderPayments(savedPayments);

   // Initialize tax categories from API with enhanced error handling
   const initializeTaxWithFallback = async () => {
     try {
       console.log('Initializing tax categories for POS...');
       await initializeTaxCategories();

       // If no tax rate is set, try to set a default one
       if (savedPaymentSummary.taxRate === 0) {
         const { getDefaultTaxCategory } = await import('../../core/utils/taxCategories');
         const defaultTax = await getDefaultTaxCategory(false);
         if (defaultTax) {
           const newPaymentSummary = { ...savedPaymentSummary, taxRate: defaultTax.rate };
           setPaymentSummary(newPaymentSummary);
           savePaymentSummary(newPaymentSummary);
           console.log(`Set default tax rate: ${defaultTax.rate}% (${defaultTax.name})`);
         }
       }
     } catch (error) {
       console.warn('Failed to initialize tax categories from API, using local fallback:', error);
       // Fallback to synchronous initialization
       const { initializeTaxCategoriesSync } = await import('../../core/utils/taxCategories');
       initializeTaxCategoriesSync();
     }
   };
   initializeTaxWithFallback();

   // Calculate and set order total with all settings
   const total = calculateOrderTotal({
     discountType: savedDiscountSettings.discountType,
     discountValue: savedDiscountSettings.discountValue,
     customPercentage: savedDiscountSettings.customPercentage,
     serviceCharges: savedPaymentSummary.serviceCharges,
     taxRate: savedPaymentSummary.taxRate,
     voucher: savedPaymentSummary.voucher,
     roundOff: savedPaymentSummary.roundOff
   });
   setOrderTotal(total);

   // Calculate payment balance
   const balance = calculateRemainingBalance(parseFloat(total.total));
   setPaymentBalance(balance);
 }, []);

 // Listen for order total updates from modals
 useEffect(() => {
   const handleOrderTotalUpdate = (event) => {
     const { orderTotal, discountSettings: newDiscountSettings, paymentSummary: newPaymentSummary } = event.detail;

     // Update states
     setOrderTotal(orderTotal);
     setDiscountSettings(newDiscountSettings);
     setPaymentSummary(newPaymentSummary);

     // Update payment balance
     const balance = calculateRemainingBalance(parseFloat(orderTotal.total));
     setPaymentBalance(balance);
   };

   window.addEventListener('orderTotalUpdated', handleOrderTotalUpdate);

   return () => {
     window.removeEventListener('orderTotalUpdated', handleOrderTotalUpdate);
   };
 }, []);

 // Listen for payment updates from modals
 useEffect(() => {
   const handlePaymentUpdate = (event) => {
     const { payments } = event.detail;
     setCurrentOrderPayments(payments);

     // Update payment balance
     const balance = calculateRemainingBalance(parseFloat(orderTotal.total));
     setPaymentBalance(balance);
   };

   window.addEventListener('paymentUpdated', handlePaymentUpdate);

   return () => {
     window.removeEventListener('paymentUpdated', handlePaymentUpdate);
   };
 }, [orderTotal.total]);

 // Handle product click to toggle in order list
 const handleProductClick = (product, event) => {
   // Check if product is already in order list
   const isInOrderList = orderItems.some(item => item.id === product.id);

   let updatedOrders;

   if (isInOrderList) {
     // If product is already in order list, remove it
     updatedOrders = removeProductFromOrders(product.id);
   } else {
     // If product is not in order list, add it
     updatedOrders = addProductToOrders(product);
   }

   // Update state
   setOrderItems(updatedOrders);

   // Calculate and set order total with all settings
   const total = calculateOrderTotal({
     discountType: discountSettings.discountType,
     discountValue: discountSettings.discountValue,
     customPercentage: discountSettings.customPercentage,
     serviceCharges: paymentSummary.serviceCharges,
     taxRate: paymentSummary.taxRate,
     voucher: paymentSummary.voucher,
     roundOff: paymentSummary.roundOff
   });
   setOrderTotal(total);

   // Add visual feedback
   if (event && event.currentTarget) {
     const element = event.currentTarget;

     // Add pulse animation class
     element.classList.add("pulse-animation");

     // Remove animation class after animation completes
     setTimeout(() => {
       element.classList.remove("pulse-animation");
     }, 300);
   }
 }

 // Handle quantity change
 const handleQuantityChange = (productId, newQuantity) => {
   // Update product quantity in local storage
   const updatedOrders = updateProductQuantity(productId, newQuantity);

   // Update state
   setOrderItems(updatedOrders);

   // Calculate and set order total with all settings
   const total = calculateOrderTotal({
     discountType: discountSettings.discountType,
     discountValue: discountSettings.discountValue,
     customPercentage: discountSettings.customPercentage,
     serviceCharges: paymentSummary.serviceCharges,
     taxRate: paymentSummary.taxRate,
     voucher: paymentSummary.voucher,
     roundOff: paymentSummary.roundOff
   });
   setOrderTotal(total);
 }

 // Handle product removal
 const handleRemoveProduct = (productId) => {
   // Remove product from local storage
   const updatedOrders = removeProductFromOrders(productId);

   // Update state
   setOrderItems(updatedOrders);

   // Calculate and set order total with all settings
   const total = calculateOrderTotal({
     discountType: discountSettings.discountType,
     discountValue: discountSettings.discountValue,
     customPercentage: discountSettings.customPercentage,
     serviceCharges: paymentSummary.serviceCharges,
     taxRate: paymentSummary.taxRate,
     voucher: paymentSummary.voucher,
     roundOff: paymentSummary.roundOff
   });
   setOrderTotal(total);
 }

 // Handle clear all products
 const handleClearOrders = () => {
   // Clear orders from local storage
   clearOrders();

   // Clear payments from local storage
   clearCurrentOrderPayments();

   // Update state
   setOrderItems([]);
   setCurrentOrderPayments([]);

   // Reset order total
   setOrderTotal({
     subtotal: '0.00',
     tax: '0.00',
     taxRate: 0,
     discount: '0.00',
     serviceCharges: '0.00',
     voucher: '0.00',
     roundOffAmount: '0.00',
     total: '0.00',
     discountType: 'percentage',
     discountValue: 0
   });

   // Reset payment balance
   setPaymentBalance({
     orderTotal: 0,
     totalPaid: 0,
     remaining: 0,
     isFullyPaid: false,
     overpaid: 0
   });
 }
  const settings = {
    dots: false,
    autoplay: false,
    slidesToShow: 8, // Increased from 6 to 8 to show more categories
    margin: 0,
    speed: 500,
    infinite: false,
    arrows: true,
    centerMode: false,
    centerPadding: '0',
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 6,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 5,
        },
      },
      {
        breakpoint: 800,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 776,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 567,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };
  const options = [
    { value: 'walkInCustomer', label: 'Walk in Customer' },
    { value: 'john', label: 'John' },
    { value: 'smith', label: 'Smith' },
    { value: 'ana', label: 'Ana' },
    { value: 'elza', label: 'Elza' },
  ];

  // const gstOptions = [
  //   { value: "choose", label: "Choose" },
  //   { value: "gst5", label: "GST 5%" },
  //   { value: "gst10", label: "GST 10%" },
  //   { value: "gst15", label: "GST 15%" },
  //   { value: "gst20", label: "GST 20%" },
  //   { value: "gst25", label: "GST 25%" },
  //   { value: "gst30", label: "GST 30%" },
  // ];
  // const numericOptions = [
  //   { value: "0", label: "0" },
  //   { value: "15", label: "15" },
  //   { value: "20", label: "20" },
  //   { value: "25", label: "25" },
  //   { value: "30", label: "30" },
  // ];

  // const percentageOptions = [
  //   { value: "0%", label: "0%" },
  //   { value: "10%", label: "10%" },
  //   { value: "15%", label: "15%" },
  //   { value: "20%", label: "20%" },
  //   { value: "25%", label: "25%" },
  //   { value: "30%", label: "30%" },
  // ];
// Update order total when order items change
useEffect(() => {
  // Calculate and set order total with all settings
  const total = calculateOrderTotal({
    discountType: discountSettings.discountType,
    discountValue: discountSettings.discountValue,
    customPercentage: discountSettings.customPercentage,
    serviceCharges: paymentSummary.serviceCharges,
    taxRate: paymentSummary.taxRate,
    voucher: paymentSummary.voucher,
    roundOff: paymentSummary.roundOff
  });
  setOrderTotal(total);

  // Update payment balance
  const balance = calculateRemainingBalance(parseFloat(total.total));
  setPaymentBalance(balance);
}, [orderItems, discountSettings.discountType, discountSettings.discountValue, discountSettings.customPercentage, paymentSummary.serviceCharges, paymentSummary.taxRate, paymentSummary.voucher, paymentSummary.roundOff]);

// Update order total when discount settings or payment summary change
useEffect(() => {
  if (orderItems.length > 0) {
    // Calculate and set order total with all settings
    const total = calculateOrderTotal({
      discountType: discountSettings.discountType,
      discountValue: discountSettings.discountValue,
      customPercentage: discountSettings.customPercentage,
      serviceCharges: paymentSummary.serviceCharges,
      taxRate: paymentSummary.taxRate,
      voucher: paymentSummary.voucher,
      roundOff: paymentSummary.roundOff
    });
    setOrderTotal(total);

    // Save settings to local storage
    saveDiscountSettings(discountSettings);
    savePaymentSummary(paymentSummary);
  }
}, [discountSettings, paymentSummary, orderItems.length]);

useEffect(() => {
    // Add page class
    document.body.classList.add("pos-page");

    return () => {
        document.body.classList.remove("pos-page");
    }
}, [Location.pathname])

  return (
    <div className="main-wrapper pos-default">
      <div className="page-wrapper pos-pg-wrapper ms-0">
        <div className="content pos-design p-0">
          <div className="row align-items-start pos-wrapper">
            {/* Products */}
            <div className="col-md-12 col-lg-7 col-xl-8">
              <div className="pos-categories tabs_wrapper pb-0">
                <div className="card pos-button">
                  <div className="d-flex align-items-center flex-wrap">
                    <Link
                      to="#"
                      className="btn btn-teal btn-md mb-xs-3"
                      data-bs-toggle="modal"
                      data-bs-target="#orders"
                    >
                      <i className="ti ti-shopping-cart me-1" />
                      View Orders
                    </Link>
                    <Link
                      to="#"
                      className="btn btn-md btn-indigo"
                      data-bs-toggle="modal"
                      data-bs-target="#reset"
                    >
                      <i className="ti ti-reload me-1" />
                      Reset
                    </Link>
                    <Link
                      to="#"
                      className="btn btn-md btn-info"
                      data-bs-toggle="modal"
                      data-bs-target="#recents"
                    >
                      <i className="ti ti-refresh-dot me-1" />
                      Transaction
                    </Link>
                  </div>
                </div>
                <div className="d-flex align-items-center justify-content-between">
                  <h4 className="mb-3">Categories</h4>
                </div>
                <Slider {...settings} className={`tabs owl-carousel pos-category ${categories.length === 0 ? 'no-data' : ''}`}>
                  {/* Always show "All Categories" option */}
                  <div onClick={()=>setActiveTab('all')} className={`owl-item ${activeTab === 'all' ? 'active' : ''}`} id="all" style={{ marginLeft: categories.length === 0 ? '0' : '' }}>
                    <Link to="#">
                      <ImageWithBasePath
                        src="assets/img/categories/category-01.svg"
                        alt="Categories"
                      />
                    </Link>
                    <h6>
                      <Link to="#">All Categories</Link>
                    </h6>
                    <span>{products.length} Items</span>
                  </div>

                  {/* Loading indicator */}
                  {loadingCategories && (
                    <div className="owl-item loading-indicator" style={{ height: '100%' }}>
                      <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <div className="spinner-border text-primary" role="status" style={{ width: '1.5rem', height: '1.5rem' }}>
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </Link>
                      <h6 style={{ visibility: 'hidden' }}>
                        <Link to="#">Placeholder</Link>
                      </h6>
                      <span style={{ visibility: 'hidden' }}>0 Items</span>
                    </div>
                  )}

                  {/* Error indicator */}
                  {categoryError && (
                    <div className="owl-item error-indicator" style={{ height: '100%' }}>
                      <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <i className="ti ti-alert-circle text-danger" style={{ fontSize: '1.5rem' }}></i>
                      </Link>
                      <h6>
                        <Link to="#" className="text-danger">Error</Link>
                      </h6>
                      <span className="text-danger">{categoryError}</span>
                    </div>
                  )}

                  {/* Map through categories from API */}
                  {categories.map((category, index) => {
                    // Create a slug/id from the category name for the tab
                    const categoryId = category.id;

                    // Calculate number of products in this category using a flexible approach
                    const categoryProductCount = products.filter(product =>
                      product.productCategoryId === categoryId ||
                      product.productCategory?.id === categoryId ||
                      product.categoryId === categoryId ||
                      product.category?.id === categoryId
                    ).length;

                    return (
                      <div
                        key={category.id || index}
                        onClick={() => setActiveTab(categoryId)}
                        className={`owl-item ${activeTab === categoryId ? 'active' : ''}`}
                        id={categoryId}
                      >
                        <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          {category.image || category.img ? (
                            <ImageWithBasePath
                              src={category.image || category.img}
                              alt={category.name}
                              style={{ width: '40px', height: '40px' }}
                            />
                          ) : (
                            <div style={{ width: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f5f5f5', borderRadius: '50%' }}>
                              <i className="ti ti-category" style={{ fontSize: '20px', color: '#666' }}></i>
                            </div>
                          )}
                        </Link>
                        <h6>
                          <Link to="#">{category.name}</Link>
                        </h6>
                        <span>{categoryProductCount} Items</span>
                      </div>
                    );
                  })}
                </Slider>
                <div className="pos-products">
                  <div className="d-flex align-items-center justify-content-between">
                    <h4 className="mb-3">Products</h4>
                    <div className="input-icon-start pos-search position-relative mb-3">
                      <span className="input-icon-addon">
                        <i className="ti ti-search" />
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Search Product"
                        value={searchQuery}
                        onChange={handleSearchChange}
                      />
                    </div>
                  </div>

                  {/* Loading indicator for products */}
                  {loadingProducts && (
                    <div className="text-center my-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading products...</span>
                      </div>
                      <p className="mt-2">Loading products...</p>
                    </div>
                  )}

                  {/* Error indicator for products */}
                  {productError && (
                    <div className="alert alert-danger" role="alert">
                      <i className="ti ti-alert-circle me-2"></i>
                      {productError}
                    </div>
                  )}
                  <div className="tabs_container">
                    <div className={`tab_content active`} data-tab="all">
                      {filteredProducts.length === 0 && !loadingProducts && !productError ? (
                        <div className="text-center my-4">
                          <i className="ti ti-box fs-3 text-muted"></i>
                          <p className="mt-2">No products found</p>
                        </div>
                      ) : (
                        <div className="row">
                          {filteredProducts.map((product, index) => {
                            let defaultUom = product.productUOM.find(uom => uom.isMainUom === true);
                            if (defaultUom == null && product.productUOM.length > 0) {
                              defaultUom = product.productUOM[0];
                            }

                            let price = defaultUom?.effectivedProductPrice?.price || 0;
                            price = price.toFixed(product.currency?.precision || 2);

                            // Check if product is in order list
                            const isInOrderList = orderItems.some(item => item.id === product.id);

                            return (
                              <div className="col-sm-6 col-md-6 col-lg-4 col-xl-3" key={product.id || index}>
                                <div
                                  className={`product-info card ${isInOrderList ? 'active' : ''}`}
                                  onClick={(e) => handleProductClick(product, e)}
                                  title={isInOrderList ? "Click to remove from order" : "Click to add to order"}
                                  tabIndex="0"
                                >
                                  <Link to="#" className="pro-img">
                                    <ImageWithBasePath
                                      src={product.image || product.img || "assets/img/products/pos-product-01.svg"}
                                      alt={product.name}
                                    />
                                    <span className={isInOrderList ? 'visible' : ''}>
                                      <i className="ti ti-circle-check-filled" />
                                    </span>
                                  </Link>
                                  <h6 className="cat-name">
                                    <Link to="#">{product.productCategory?.name || 'Uncategorized'}</Link>
                                  </h6>
                                  <h6 className="product-name">
                                    <Link to="#">{product.name}</Link>
                                  </h6>
                                  <div className="d-flex align-items-center justify-content-between price">
                                    <span>
                                      {defaultUom?.effectivedProductPrice?.fractionQty || 1} { defaultUom?.uomPrimary?.name || 'Pcs' }
                                    </span>
                                    <p>{formatCurrency(price)}</p>
                                  </div>
                                  {isInOrderList && (
                                    <div className="added-badge">
                                      <span className="badge bg-success">
                                        <i className="ti ti-check"></i>
                                        <i className="ti ti-x remove-icon" title="Click to remove"></i>
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* /Products */}
            {/* Order Details */}
            <div className="col-md-12 col-lg-5 col-xl-4 ps-0 theiaStickySidebar d-lg-flex">
                <aside className="product-order-list bg-secondary-transparent flex-fill">
                    <div className="card">
                        <div className="card-body">
                            <div className="order-head d-flex align-items-center justify-content-between w-100">
                                <div>
                                    <h3>Order List</h3>
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <span className="badge badge-dark fs-10 fw-medium badge-xs">
                                        #ORD123
                                    </span>
                                    <Link className="link-danger fs-16" to="#">
                                        <i className="ti ti-trash-x-filled" />
                                    </Link>
                                </div>
                            </div>
                            <div className="customer-info block-section">
                                <h5 className="mb-2">Customer Information</h5>
                                <div className="d-flex align-items-center gap-2">
                                    <div className="flex-grow-1">
                                        <Select
                                            options={options}
                                            classNamePrefix="react-select select"
                                            placeholder="Choose a Name"
                                            defaultValue={options[0]}
                                        />
                                    </div>
                                    <Link
                                        to="#"
                                        className="btn btn-teal btn-icon fs-20"
                                        data-bs-toggle="modal"
                                        data-bs-target="#create"
                                    >
                                        <i className="ti ti-user-plus" />
                                    </Link>
                                    <Link
                                        to="#"
                                        className="btn btn-info btn-icon fs-20"
                                        data-bs-toggle="modal"
                                        data-bs-target="#barcode"
                                    >
                                        <i className="ti ti-scan" />
                                    </Link>
                                </div>
                                {showAlert &&
                                    <div className="customer-item border border-orange bg-orange-100 d-flex align-items-center justify-content-between flex-wrap gap-2 mt-3">
                                        <div>
                                            <h6 className="fs-16 fw-bold mb-1">James Anderson</h6>
                                            <div className="d-inline-flex align-items-center gap-2 customer-bonus">
                                                <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                    Bonus :
                                                    <span className="badge bg-cyan fs-13 fw-bold p-1">
                                                        148
                                                    </span>{" "}
                                                </p>
                                                <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                    Loyality :
                                                    <span className="badge bg-teal fs-13 fw-bold p-1">
                                                        RM 20.00
                                                    </span>{" "}
                                                </p>
                                            </div>
                                        </div>
                                        <Link
                                            to="#"
                                            className="btn btn-orange btn-sm"
                                        >
                                            Apply
                                        </Link>
                                        <Link to="#" className="close-icon" onClick={() => setShowAlert(false)}>
                                            <i className="ti ti-x" />
                                        </Link>
                                    </div>}
                            </div>
                            <div className="product-added block-section">
                                <div className="head-text d-flex align-items-center justify-content-between mb-3">
                                    <div className="d-flex align-items-center">
                                        <h5 className="me-2">Order Details</h5>
                                        <div className="badge bg-light text-gray-9 fs-12 fw-semibold py-2 border rounded">
                                            Items : <span className="text-teal">{orderItems.length}</span>
                                        </div>
                                    </div>
                                    <Link
                                        to="#"
                                        className="d-flex align-items-center clear-icon fs-10 fw-medium"
                                        onClick={handleClearOrders}
                                    >
                                        Clear all
                                    </Link>
                                </div>
                                <div className="product-wrap">
                                    {orderItems.length === 0 ? (
                                        <div className="empty-cart">
                                            <div className="fs-24 mb-1">
                                                <i className="ti ti-shopping-cart" />
                                            </div>
                                            <p className="fw-bold">No Products Selected</p>
                                        </div>
                                    ) : (
                                        <div className="product-list border-0 p-0">
                                            <div className="table-responsive">
                                                <table className="table table-borderless">
                                                    <thead>
                                                        <tr>
                                                            <th className="fw-bold bg-light">Item</th>
                                                            <th className="fw-bold bg-light">QTY</th>
                                                            <th className="fw-bold bg-light text-end">Cost</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {orderItems.map((item, index) => (
                                                            <tr key={item.id || index}>
                                                                <td>
                                                                    <div className="d-flex align-items-center">
                                                                        <Link
                                                                            className="delete-icon"
                                                                            to="#"
                                                                            onClick={() => handleRemoveProduct(item.id)}
                                                                        >
                                                                            <i className="ti ti-trash-x-filled" />
                                                                        </Link>
                                                                        <h6 className="fs-13 fw-normal">
                                                                            <Link
                                                                                to="#"
                                                                                className="link-default"
                                                                            >
                                                                                {item.name}
                                                                            </Link>
                                                                        </h6>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter
                                                                            defaultValue={item.quantity}
                                                                            onChange={(value) => handleQuantityChange(item.id, value)}
                                                                        />
                                                                    </div>
                                                                </td>
                                                                <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                    {formatCurrency(item.price * item.quantity)}
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                {discountSettings.customPercentage > 0 && (
                                  <div className="discount-item d-flex align-items-center justify-content-between bg-purple-transparent mt-3 flex-wrap gap-2">
                                      <div className="d-flex align-items-center">
                                          <span className="bg-purple discount-icon br-5 flex-shrink-0 me-2">
                                              <img src="assets/img/icons/discount-icon.svg" alt="img" />
                                          </span>
                                          <div>
                                              <h6 className="fs-14 fw-bold text-purple mb-1">
                                                  Custom Discount {discountSettings.customPercentage}%
                                              </h6>
                                              <p className="mb-0">
                                                  Applied to all items in order
                                              </p>
                                          </div>
                                      </div>
                                      <Link
                                        to="#"
                                        className="close-icon"
                                        onClick={() => {
                                          const newSettings = { ...discountSettings, customPercentage: 0 };
                                          setDiscountSettings(newSettings);
                                          saveDiscountSettings(newSettings);

                                          // Recalculate order total
                                          const total = calculateOrderTotal({
                                            discountType: newSettings.discountType,
                                            discountValue: newSettings.discountValue,
                                            customPercentage: 0,
                                            serviceCharges: paymentSummary.serviceCharges,
                                            taxRate: paymentSummary.taxRate,
                                            voucher: paymentSummary.voucher,
                                            roundOff: paymentSummary.roundOff
                                          });
                                          setOrderTotal(total);
                                        }}
                                      >
                                          <i className="ti ti-trash" />
                                      </Link>
                                  </div>
                                )}
                            </div>
                            <div className="order-total bg-total bg-white p-0">
                                <h5 className="mb-3">Payment Summary</h5>
                                <table className="table table-responsive table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>
                                                Tax
                                                {orderTotal.taxRate > 0 && (
                                                  <span className="ms-2 badge bg-info">
                                                    {orderTotal.taxRate}%
                                                  </span>
                                                )}
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#order-tax"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.tax)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Voucher
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#coupon-code"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.voucher)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <span className="text-danger">Discount</span>
                                                {discountSettings.customPercentage > 0 && (
                                                  <span className="ms-2 badge bg-info">
                                                    {discountSettings.customPercentage}%
                                                  </span>
                                                )}
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#discount"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-danger text-end">{formatCurrency(orderTotal.discount)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Service Charges
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#shipping-cost"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.serviceCharges)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div className="form-check form-switch">
                                                    <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        role="switch"
                                                        id="round"
                                                        checked={paymentSummary.roundOff}
                                                        onChange={(e) => {
                                                          const newPaymentSummary = { ...paymentSummary, roundOff: e.target.checked };
                                                          setPaymentSummary(newPaymentSummary);
                                                          savePaymentSummary(newPaymentSummary);

                                                          // Recalculate order total
                                                          const total = calculateOrderTotal({
                                                            discountType: discountSettings.discountType,
                                                            discountValue: discountSettings.discountValue,
                                                            customPercentage: discountSettings.customPercentage,
                                                            serviceCharges: newPaymentSummary.serviceCharges,
                                                            taxRate: newPaymentSummary.taxRate,
                                                            voucher: newPaymentSummary.voucher,
                                                            roundOff: e.target.checked
                                                          });
                                                          setOrderTotal(total);
                                                        }}
                                                    />
                                                    <label className="form-check-label" htmlFor="round">
                                                        Rounding Adjustment
                                                    </label>
                                                </div>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.roundOffAmount)}</td>
                                        </tr>
                                        <tr>
                                            <td>Sub Total</td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.subtotal)}</td>
                                        </tr>
                                        <tr>
                                            <td className="fw-bold border-top border-dashed">
                                                Total Payable
                                            </td>
                                            <td className="text-gray-9 fw-bold text-end border-top border-dashed">
                                                {formatCurrency(orderTotal.total)}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {/* Payment Listing Section */}
                    {currentOrderPayments.length > 0 && (
                      <div className="card payment-listing">
                        <div className="card-body">
                          <div className="d-flex align-items-center justify-content-between mb-3">
                            <h5 className="mb-0">Payment History</h5>
                            <span className="badge bg-info">
                              {currentOrderPayments.length} Payment{currentOrderPayments.length !== 1 ? 's' : ''}
                            </span>
                          </div>
                          <div className="payment-list">
                            {currentOrderPayments.map((payment, index) => (
                              <div key={payment.id} className="payment-item-row d-flex align-items-center justify-content-between p-2 mb-2 bg-light rounded">
                                <div className="payment-info">
                                  <div className="d-flex align-items-center gap-2">
                                    <span className="badge bg-success fs-10">#{index + 1}</span>
                                    <span className="fw-medium text-capitalize">{payment.paymentMethod}</span>
                                    {payment.paymentType && payment.paymentType !== payment.paymentMethod && (
                                      <span className="text-muted">({payment.paymentType})</span>
                                    )}
                                  </div>
                                  <small className="text-muted">
                                    {new Date(payment.timestamp).toLocaleTimeString()}
                                  </small>
                                </div>
                                <div className="payment-amount text-end">
                                  <div className="fw-bold text-success">
                                    {formatPaymentCurrency(payment.amount)}
                                  </div>
                                  {payment.change > 0 && (
                                    <small className="text-muted">
                                      Change: {formatPaymentCurrency(payment.change)}
                                    </small>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Payment Balance Summary */}
                          <div className="payment-balance-summary mt-3 p-3 bg-primary-transparent rounded">
                            <div className="row g-2">
                              <div className="col-6">
                                <div className="text-center">
                                  <small className="text-muted d-block">Total Paid</small>
                                  <span className="fw-bold text-success">
                                    {formatPaymentCurrency(paymentBalance.totalPaid)}
                                  </span>
                                </div>
                              </div>
                              <div className="col-6">
                                <div className="text-center">
                                  <small className="text-muted d-block">
                                    {paymentBalance.remaining > 0 ? 'Remaining' : paymentBalance.remaining < 0 ? 'Overpaid' : 'Fully Paid'}
                                  </small>
                                  <span className={`fw-bold ${paymentBalance.remaining > 0 ? 'text-warning' : paymentBalance.remaining < 0 ? 'text-info' : 'text-success'}`}>
                                    {formatPaymentCurrency(Math.abs(paymentBalance.remaining))}
                                  </span>
                                </div>
                              </div>
                            </div>
                            {paymentBalance.isFullyPaid && (
                              <div className="text-center mt-2">
                                <span className="badge bg-success">
                                  <i className="ti ti-check me-1"></i>
                                  Order Fully Paid
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="card payment-method">
                        <div className="card-body">
                            <h5 className="mb-3">Select Payment</h5>
                            <div className="row align-items-center methods g-2">
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#payment-cash"
                                    >
                                        <img
                                            src="assets/img/icons/cash-icon.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Cash</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#payment-card"
                                    >
                                        <img
                                            src="assets/img/icons/card.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Card</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#payment-points"
                                    >
                                        <img
                                            src="assets/img/icons/points.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Points</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#payment-deposit"
                                    >
                                        <img
                                            src="assets/img/icons/deposit.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Deposit</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#payment-cheque"
                                    >
                                        <img
                                            src="assets/img/icons/cheque.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Cheque</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#gift-payment"
                                    >
                                        <img
                                            src="assets/img/icons/giftcard.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Gift Card</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#scan-payment"
                                    >
                                        <img
                                            src="assets/img/icons/scan-icon.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Scan</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                    >
                                        <img
                                            src="assets/img/icons/paylater.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Pay Later</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                    >
                                        <img
                                            src="assets/img/icons/external.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">External</p>
                                    </Link>
                                </div>
                                <div className="col-sm-6 col-md-4 d-flex">
                                    <Link
                                        to="#"
                                        className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                        data-bs-toggle="modal"
                                        data-bs-target="#split-payment"
                                    >
                                        <img
                                            src="assets/img/icons/split-bill.svg"
                                            className="me-2"
                                            alt="img"
                                        />
                                        <p className="fs-14 fw-medium">Split Bill</p>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="btn-row d-flex align-items-center justify-content-between gap-3">
                        <Link
                            to="#"
                            className="btn btn-white d-flex align-items-center justify-content-center flex-fill m-0"
                            data-bs-toggle="modal"
                            data-bs-target="#hold-order"
                        >
                            <i className="ti ti-printer me-2" />
                            Print Order
                        </Link>
                        <Link
                            to="#"
                            className="btn btn-secondary d-flex align-items-center justify-content-center flex-fill m-0"
                            data-bs-toggle="modal"
                            data-bs-target="#payment-completed"
                        >
                            <i className="ti ti-shopping-cart me-2" />
                            Place Order
                        </Link>
                    </div>
                </aside>
            </div>
            {/* /Order Details */}
          </div>
        </div>
      </div>
      <PosModals/>
      <TaxMappingStatus />
    </div>
  );
};

export default Pos;
